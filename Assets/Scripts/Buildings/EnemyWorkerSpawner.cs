using Common;
using Managers;
using NaughtyAttributes;
using System.Collections.Generic;
using System.Linq;
using UnitParts.Controllers;
using UnitParts.GroupManagement;
using UnitParts.Interfaces;
using UnitParts.Orders;
using UnityEngine;

namespace Buildings
{
    public enum EnemyUnitType
    {
        Worker,
        ResourceThief
    }

    public class EnemyWorkerSpawner : MonoBehaviour
    {
        private const float TARGET_COOLDOWN = 5f; // Seconds before a conveyor can be targeted again
        [SerializeField] private int maxUnits = 5;
        [SerializeField] private int maxResourceThieves = 2;

        [SerializeField] private float spawnInterval = 10f;
        [SerializeField] private float resourceThiefSpawnMultiplier = 0.1f;

        [SerializeField] private float range = 20f;

        [SerializeField] [Required] private UnitController unitPrefab;
        [SerializeField] [Required] private UnitController resourceThiefPrefab;

        [SerializeField] [ProgressBar("Time Since Spawned", "spawnInterval")]
        private float timeSinceSpawned;

        [SerializeField] [ProgressBar("Time Since Thief Spawned", "ThiefSpawnInterval")]
        private float timeSinceThiefSpawned;

        private readonly AssignedUnitsGroup _assignedUnitsGroup = new();

        // Track recently targeted conveyors to avoid immediate re-targeting
        private readonly Dictionary<ConveyorBelt, float> _recentlyTargeted = new();
        private readonly AssignedUnitsGroup _resourceThievesGroup = new();
        private Building _building;
        private IBuildingManager _buildingManager;
        private IEnemyManager _enemyManager;
        private ITeamDamageable _selfIDamageable;

        private float ThiefSpawnInterval => spawnInterval * resourceThiefSpawnMultiplier;

        private void Start()
        {
            _building = GetComponent<Building>();
            _enemyManager = ServiceLocator.Get<IEnemyManager>();
            _buildingManager = ServiceLocator.Get<IBuildingManager>();

            // Subscribe to resource thief destruction events for cleanup
            _resourceThievesGroup.OnUnassignment += OnResourceThiefDestroyed;
        }

        private void Update()
        {
            // Handle regular worker spawning
            if (_assignedUnitsGroup.Units.Count < maxUnits && timeSinceSpawned >= spawnInterval)
            {
                timeSinceSpawned = 0f;
                SpawnUnit(EnemyUnitType.Worker);
            }
            else if (timeSinceSpawned < spawnInterval)
            {
                timeSinceSpawned += Time.deltaTime;
            }

            // Handle resource thief spawning
            if (_resourceThievesGroup.Units.Count < maxResourceThieves && timeSinceThiefSpawned >= ThiefSpawnInterval)
            {
                timeSinceThiefSpawned = 0f;
                SpawnUnit(EnemyUnitType.ResourceThief);
            }
            else if (timeSinceThiefSpawned < ThiefSpawnInterval)
            {
                timeSinceThiefSpawned += Time.deltaTime;
            }

            ControlItsUnits();
            ControlResourceThieves();
        }

        private void OnEnable() => _selfIDamageable = gameObject.GetComponent<ITeamDamageable>();

        private void OnDisable()
        {
            _enemyManager?.AddToGlobalPool(_assignedUnitsGroup.Units.ToArray());
            _assignedUnitsGroup.UnassignAll();

            // Destroy resource thieves instead of adding them to global pool
            DestroyResourceThieves();
        }

        private void OnDestroy()
        {
            // Unsubscribe from events
            _resourceThievesGroup.OnUnassignment -= OnResourceThiefDestroyed;

            _enemyManager?.AddToGlobalPool(_assignedUnitsGroup.Units.ToArray());
            _assignedUnitsGroup.UnassignAll();

            // Destroy resource thieves instead of adding them to global pool
            DestroyResourceThieves();
        }

        private void OnDrawGizmosSelected()
        {
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(transform.position, range);
            Gizmos.color = Color.green;
            foreach (var unit in _assignedUnitsGroup.Units)
            {
                if (unit != null)
                {
                    Gizmos.DrawLine(transform.position, unit.transform.position);
                }
            }

            Gizmos.color = Color.blue;
            foreach (var thief in _resourceThievesGroup.Units)
            {
                if (thief != null)
                {
                    Gizmos.DrawLine(transform.position, thief.transform.position);
                }
            }
        }

        /// <summary>
        ///     Destroy all resource thief units when spawner is destroyed
        /// </summary>
        private void DestroyResourceThieves()
        {
            var thieves = _resourceThievesGroup.Units.ToArray();
            _resourceThievesGroup.UnassignAll();

            foreach (var thief in thieves)
            {
                if (thief != null)
                {
                    Debug.Log($"Destroying resource thief {thief.name} due to spawner destruction");
                    Destroy(thief.gameObject);
                }
            }
        }

        private void ControlItsUnits()
        {
            var damageableInRange = IDamageableExtensions.InRange(transform.position, range);
            var enemies = damageableInRange.GetEnemies(_selfIDamageable);

            foreach (var unit in _assignedUnitsGroup.Units)
            {
                if (unit.unitCommander.CurrentOrder != null)
                {
                    continue;
                }

                if (unit.gunController.InCombat)
                {
                    continue;
                }

                var target = enemies.SortByDistance(unit.Position).FirstOrDefault();
                if (target != null)
                {
                    var attackOrder = OrderUtils.CreateDirectAttackOrder(unit, target);
                    unit.unitCommander.IssueOrder(attackOrder);
                    continue;
                }

                // If there are no enemies, move to a random position within the range
                var randomPosition = gameObject.transform.position + (Random.insideUnitSphere * range);
                randomPosition.z = 0f;

                var returnOrder = OrderUtils.CreateMoveOrder(unit, randomPosition);
                unit.unitCommander.IssueOrder(returnOrder);
            }
        }

        private void ControlResourceThieves()
        {
            foreach (var thief in _resourceThievesGroup.Units)
            {
                if (thief.unitCommander.CurrentOrder != null)
                {
                    continue;
                }

                // Find nearest conveyor belt with resources
                var targetConveyor = FindNearestActiveConveyorBelt(thief.Position);
                if (targetConveyor != null)
                {
                    var thiefOrder = ResourceThiefOrder.Get();
                    thiefOrder.Initialize(this, targetConveyor, transform.position, thief);
                    thief.unitCommander.IssueOrder(thiefOrder);
                }
                else
                {
                    // No conveyor belts found, move randomly within range
                    var randomPosition = gameObject.transform.position + (Random.insideUnitSphere * range);
                    randomPosition.z = 0f;
                    var returnOrder = OrderUtils.CreateMoveOrder(thief, randomPosition);
                    thief.unitCommander.IssueOrder(returnOrder);
                }
            }
        }

        private ConveyorBelt FindNearestActiveConveyorBelt(Vector3 fromPosition)
        {
            ConveyorBelt nearestConveyor = null;
            var nearestDistance = float.MaxValue;

            if (_buildingManager?.PlacedObjects == null)
            {
                return null;
            }

            // Clean up old entries from recently targeted dictionary
            var keysToRemove = new List<ConveyorBelt>();
            foreach (var kvp in _recentlyTargeted)
            {
                if (Time.time - kvp.Value > TARGET_COOLDOWN)
                {
                    keysToRemove.Add(kvp.Key);
                }
            }

            foreach (var key in keysToRemove)
            {
                _recentlyTargeted.Remove(key);
            }

            foreach (var placedObject in _buildingManager.PlacedObjects)
            {
                if (placedObject == null)
                {
                    continue;
                }

                var conveyor = placedObject.GetComponent<ConveyorBelt>();
                if (conveyor == null || conveyor.Team == _selfIDamageable.Team)
                {
                    continue;
                }

                // Check if conveyor has resources
                if (conveyor.CurrentlyHeldResource == null)
                {
                    continue;
                }

                // Skip recently targeted conveyors (unless no other options)
                if (_recentlyTargeted.ContainsKey(conveyor))
                {
                    continue;
                }

                var distance = Vector3.Distance(fromPosition, conveyor.transform.position);
                if (distance < nearestDistance)
                {
                    nearestDistance = distance;
                    nearestConveyor = conveyor;
                }
            }

            // If no conveyor found (all recently targeted), find the nearest regardless of cooldown
            if (nearestConveyor == null)
            {
                foreach (var placedObject in _buildingManager.PlacedObjects)
                {
                    if (placedObject == null)
                    {
                        continue;
                    }

                    var conveyor = placedObject.GetComponent<ConveyorBelt>();
                    if (conveyor == null || conveyor.Team == _selfIDamageable.Team)
                    {
                        continue;
                    }

                    // Check if conveyor has resources
                    if (conveyor.CurrentlyHeldResource == null)
                    {
                        continue;
                    }

                    var distance = Vector3.Distance(fromPosition, conveyor.transform.position);
                    if (distance < nearestDistance)
                    {
                        nearestDistance = distance;
                        nearestConveyor = conveyor;
                    }
                }
            }

            // Mark the selected conveyor as recently targeted
            if (nearestConveyor != null)
            {
                _recentlyTargeted[nearestConveyor] = Time.time;
            }

            return nearestConveyor;
        }

        /// <summary>
        ///     Called when a resource thief is destroyed to ensure proper cleanup
        /// </summary>
        private void OnResourceThiefDestroyed(UnitController destroyedThief)
        {
            if (destroyedThief == null)
            {
                return;
            }

            // Cancel any ongoing orders to ensure conveyor cleanup
            destroyedThief.unitCommander?.CancelAllOrders(true);

            Debug.Log($"Resource thief destroyed, cleanup completed for {destroyedThief.name}");
        }

        private void SpawnUnit(EnemyUnitType unitType)
        {
            var prefabToSpawn = unitType == EnemyUnitType.ResourceThief ? resourceThiefPrefab : unitPrefab;

            if (!prefabToSpawn)
            {
                Debug.LogError($"{unitType} prefab is not assigned.");
                return;
            }

            prefabToSpawn.Team = _selfIDamageable.Team;

            var unit = Instantiate(prefabToSpawn, transform.position, Quaternion.identity);

            unit.GetComponent<UnitInitializer>().InitializePreparedStats();
            unit.GetComponent<UnitInitializer>().Enemize();

            if (unitType == EnemyUnitType.ResourceThief)
            {
                _resourceThievesGroup.AssignUnit(unit);
            }
            else
            {
                _assignedUnitsGroup.AssignUnit(unit);
            }
        }
    }
}
