using Common;
using Managers;
using System;
using UnitParts.Interfaces;
using UnityEngine;

namespace UnitParts.Orders
{
    /// <summary>
    /// Simple order that stops all unit movement while preserving combat functionality.
    /// The unit will immediately cease all movement and remain stationary until the order
    /// is cancelled. Gun targeting, combat, and other non-movement systems continue to operate normally.
    /// </summary>
    /// <remarks>
    /// Behavior:
    /// - Immediately calls MovementController.StopMovement() to halt all movement
    /// - Does not interfere with gun targeting, combat, or other unit systems
    /// - Order completes immediately but remains active (blocking order queue advancement)
    /// - Unit will not move even if enemies are detected or other stimuli occur
    /// - Ideal for defensive positions, guard duty, or temporary movement stops
    ///
    /// Performance: Pooled object, minimal overhead, single method call execution
    /// Completion: Completes immediately but stays active until cancelled
    /// Interruption: Can be cancelled to restore movement capability
    ///
    /// Usage Examples:
    /// - Guard position: var stay = StayStillOrder.Get(); stay.Initialize(unit, unit.movementController);
    /// - Emergency stop: Used to immediately halt unit movement in critical situations
    /// - Defensive stance: Combined with other orders for stationary defense
    /// </remarks>
    [Serializable]
    public class StayStillOrder : BaseOrder
    {
        public IMovementController MovementController;

        public StayStillOrder Initialize(MonoBehaviour issuer, IMovementController movementController,
            Action onComplete = null, Action onCancel = null)
        {
            base.Initialize(issuer, onComplete, onCancel);
            MovementController = movementController;
            return this;
        }

        public override void Dispose()
        {
            var orderPoolManager = ServiceLocator.Get<IOrderPoolManager>();
            orderPoolManager?.StayStillOrderPool?.Return(this);
        }

        public static new StayStillOrder Get()
        {
            var orderPoolManager = ServiceLocator.Get<IOrderPoolManager>();
            return orderPoolManager?.StayStillOrderPool?.Get() ?? new StayStillOrder();
        }

        public override void Execute() =>
            // Stop all movement but allow combat to continue
            MovementController.StopMovement();

        // This order completes immediately but stays active
        // The unit will remain still until this order is cancelled
        // Combat (GunController) will continue to function normally
        public override void Cancel() =>
            // No special cleanup needed for staying still
            InvokeOnCancel();
    }
}
