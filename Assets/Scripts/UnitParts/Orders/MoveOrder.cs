using Common;
using Managers;
using System;
using UnitParts.Interfaces;
using UnityEngine;

namespace UnitParts.Orders
{
    /// <summary>
    /// Basic movement order that moves a unit to a specific position using pathfinding.
    /// This is the fundamental building block for all movement-based orders in the system.
    /// Supports both simple movement and movement with stopping distance for precise positioning.
    /// </summary>
    /// <remarks>
    /// Behavior:
    /// - Uses the unit's MovementController for pathfinding and movement execution
    /// - Supports optional stopping distance for maintaining distance from target
    /// - Automatically completes when unit reaches target position within stopping distance
    /// - Can be cancelled to immediately stop movement
    ///
    /// Performance: Pooled object, minimal overhead, direct MovementController integration
    /// Completion: When unit reaches target position within stopping distance
    /// Interruption: Can be cancelled, will call MovementController.StopMovement()
    ///
    /// Usage Examples:
    /// - Basic movement: var move = MoveOrder.Get(); move.Initialize(unit, targetPos, movementController);
    /// - With stopping distance: move.Initialize(unit, targetPos, 2f, movementController);
    /// - Typically wrapped in decorators for additional behavior (timing, tracking, etc.)
    /// </remarks>
    [Serializable]
    public class MoveOrder : BaseOrder
    {
        /// <summary>
        /// The target position in world coordinates where the unit should move.
        /// </summary>
        public Vector3 TargetPosition;

        /// <summary>
        /// The stopping distance in world units from the target position.
        /// When set to -1, uses the MovementController's default stopping behavior.
        /// Range: -1 (default) or 0.0-10.0 units recommended for most use cases.
        /// </summary>
        public float StoppingDistance;

        /// <summary>
        /// The movement controller responsible for executing the actual movement.
        /// Must be valid and belong to the same unit that issued this order.
        /// </summary>
        public IMovementController MovementController;

        public MoveOrder Initialize(MonoBehaviour issuer, Vector3 targetPosition,
            IMovementController movementController,
            Action onComplete = null, Action onCancel = null)
        {
            base.Initialize(issuer, onComplete, onCancel);

            TargetPosition = targetPosition;
            StoppingDistance = -1;
            MovementController = movementController;

            return this;
        }

        public MoveOrder Initialize(MonoBehaviour issuer, Vector3 targetPosition, float stoppingDistance,
            IMovementController movementController,
            Action onComplete = null, Action onCancel = null)
        {
            base.Initialize(issuer, onComplete, onCancel);

            TargetPosition = targetPosition;
            StoppingDistance = stoppingDistance;
            MovementController = movementController;

            return this;
        }

        public override void Dispose()
        {
            var orderPoolManager = ServiceLocator.Get<IOrderPoolManager>();
            orderPoolManager?.MoverOrderPool.Return(this);
        }

        public static new MoveOrder Get()
        {
            var orderPoolManager = ServiceLocator.Get<IOrderPoolManager>();
            return orderPoolManager?.MoverOrderPool.Get();
        }

        public override void Execute()
        {
            if (StoppingDistance < 0)
            {
                MovementController.MoveTo(TargetPosition, InvokeOnComplete);
            }
            else
            {
                MovementController.MoveTo(TargetPosition, StoppingDistance, InvokeOnComplete);
            }
        }

        public override void Cancel()
        {
            MovementController.StopMovement();
            InvokeOnCancel();
        }
    }
}
