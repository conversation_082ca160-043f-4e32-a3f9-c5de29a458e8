using Common;
using Managers;
using System;
using UnitParts.Controllers;
using UnitParts.Interfaces;
using UnitParts.Orders.Decorators;
using UnityEngine;

namespace UnitParts.Orders
{
    /// <summary>
    /// Defensive order that positions a unit at a specific location and engages enemies within range.
    /// The unit moves to the defend position, then continuously scans for enemies within detection radius.
    /// Enemies are engaged only if they are within the defend radius, ensuring the unit doesn't chase too far.
    /// After combat, the unit returns to the defend position to maintain defensive posture.
    /// </summary>
    /// <remarks>
    /// Behavior:
    /// - Moves to defend position using standard movement
    /// - Continuously scans for enemies within detection radius every 0.2 seconds
    /// - Engages enemies only if they are within defend radius from defend position
    /// - Uses ConditionalDecorator to enforce radius constraints during combat
    /// - Returns to defend position after combat if moved during engagement
    /// - Never completes automatically, maintains defensive posture indefinitely
    ///
    /// Performance: Pooled object, moderate overhead due to continuous detection and constraint checking
    /// Completion: Never completes automatically, must be cancelled to stop defending
    /// Interruption: Can be cancelled, will stop defending and allow new orders
    ///
    /// Usage Examples:
    /// - Chokepoint defense: var defend = DefendOrder.Get(); defend.Initialize(unit, chokePoint, 6f, 10f, unit);
    /// - Base defense: defend.Initialize(unit, baseEntrance, 8f, 12f, unit);
    /// - Area denial: Used to control specific map areas with radius-constrained engagement
    /// </remarks>
    [Serializable]
    public class DefendOrder : BaseOrder
    {
        public Vector3 DefendPosition;
        public float DefendRadius;
        public float DetectionRadius;
        public UnitController UnitController;

        private IBaseOrder _currentSubOrder;
        private bool _isDefending = true;

        public DefendOrder Initialize(MonoBehaviour issuer, Vector3 defendPosition, float defendRadius,
            float detectionRadius, UnitController unitController,
            Action onComplete = null, Action onCancel = null)
        {
            base.Initialize(issuer, onComplete, onCancel);

            DefendPosition = defendPosition;
            DefendRadius = defendRadius;
            DetectionRadius = detectionRadius;
            UnitController = unitController;

            return this;
        }

        public override void Dispose()
        {
            var orderPoolManager = ServiceLocator.Get<IOrderPoolManager>();
            orderPoolManager?.DefendOrderPool?.Return(this);
        }

        public static new DefendOrder Get()
        {
            var orderPoolManager = ServiceLocator.Get<IOrderPoolManager>();
            return orderPoolManager?.DefendOrderPool?.Get() ?? new DefendOrder();
        }

        public override void Execute() => StartDefendBehavior();

        private void StartDefendBehavior()
        {
            // Create a stay still order at the defend position
            var stayOrder = CreateStayAtPositionOrder();

            // Wrap it with enemy detection
            var detectionDecorator = new EnemyDetectionDecorator(stayOrder)
            {
                detectionRadius = DetectionRadius,
                onEnemyDetected = OnEnemyDetected,
                onNoEnemiesDetected = OnNoEnemiesDetected
            };

            _currentSubOrder = detectionDecorator;
            _currentSubOrder.Execute();
        }

        private IBaseOrder CreateStayAtPositionOrder()
        {
            // If not at defend position, move there first
            var distanceToDefendPos = Vector3.Distance(UnitController.Position, DefendPosition);
            if (distanceToDefendPos > 0.5f)
            {
                return OrderUtils.MoveToPosition(UnitController, DefendPosition);
            }

            return CreateStayStillOrder();
        }

        private IBaseOrder CreateStayStillOrder()
        {
            var stayStillOrder = StayStillOrder.Get();
            stayStillOrder.Initialize(UnitController, UnitController.movementController);
            return stayStillOrder;
        }

        private void OnEnemyDetected(ITeamDamageable enemy)
        {
            if (!_isDefending)
            {
                return;
            }

            _isDefending = false;
            _currentSubOrder?.Cancel();

            // Create attack order with radius constraint
            var attackOrder = CreateConstrainedAttackOrder(enemy);
            _currentSubOrder = attackOrder;
            _currentSubOrder.Execute();
        }

        private void OnNoEnemiesDetected()
        {
            if (_isDefending)
            {
                return;
            }

            _isDefending = true;
            _currentSubOrder?.Cancel();

            // Return to defend position
            StartDefendBehavior();
        }

        private IBaseOrder CreateConstrainedAttackOrder(ITeamDamageable target)
        {
            var attackOrder = OrderUtils.CreateDirectAttackOrder(UnitController, target);

            // Add radius constraint using conditional decorator
            var radiusConstraintDecorator = new ConditionalDecorator(attackOrder)
            {
                condition = () =>
                {
                    // Cancel attack if target is too far from defend position
                    var distanceFromDefendPos = Vector3.Distance(target.Position, DefendPosition);
                    return distanceFromDefendPos > DefendRadius || target.IsDestroyed;
                },
                onCondition = () => OnNoEnemiesDetected()
            };

            return radiusConstraintDecorator;
        }

        public override void Cancel()
        {
            _currentSubOrder?.Cancel();
            InvokeOnCancel();
        }
    }
}
