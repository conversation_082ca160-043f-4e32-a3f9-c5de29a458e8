using System;
using System.Collections;
using UnitParts.Interfaces;
using UnityEngine;

namespace UnitParts.Orders.Decorators
{
    public class TimedDecorator : BaseOrderDecorator
    {
        private Coroutine _timeoutCoroutine;

        public TimedDecorator(IBaseOrder order) : base(order) { }

        public float timeLimit { get; set; }
        public Action onTimeout { get; set; }

        public override void Execute()
        {
            _timeoutCoroutine = (WrappedOrder.Issuer as MonoBehaviour)?.StartCoroutine(TimeoutRoutine());
            base.Execute();
        }

        private IEnumerator TimeoutRoutine()
        {
            yield return new WaitForSeconds(timeLimit);
            onTimeout?.Invoke();
            base.Cancel();
        }

        public override void Cancel()
        {
            (WrappedOrder.Issuer as MonoBehaviour)?.StopCoroutine(_timeoutCoroutine);
            base.Cancel();
        }
    }
}
