using UnitParts.Controllers;
using UnitParts.Interfaces;
using UnitParts.Orders;
using UnitParts.Orders.Decorators;
using UnityEngine;

namespace Common
{
    public abstract class OrderUtils
    {
        public static IBaseOrder DirectAttackOrder(UnitController unit, ITeamDamageable target)
        {
            // Calculate optimal attack position based on gun range
            var optimalPosition = CalculateOptimalAttackPosition(unit, target);
            var moveOrder = MoveToPosition(unit, optimalPosition); // Small stopping distance for positioning

            var timedMoveOrder = new TimedDecorator(moveOrder) { timeLimit = 1.0f }; // Increased timeout
            timedMoveOrder.onTimeout += () =>
            {
                if (target.IsDestroyed)
                {
                    return;
                }

                // repeat until target is dead
                var attackOrder = DirectAttackOrder(unit, target);
                unit.unitCommander.IssueOrder(attackOrder);
            };

            var targetedTimedMoveOrder = new GunTargetDecorator(timedMoveOrder) { target = target };
            return targetedTimedMoveOrder;
        }

        /// <summary>
        ///     Calculate optimal attack position that maintains proper distance from target for effective gun targeting
        /// </summary>
        private static Vector3 CalculateOptimalAttackPosition(UnitController unit, ITeamDamageable target)
        {
            if (unit.gunController?.gun == null)
            {
                // No gun, move directly to target
                return target.Position;
            }

            var gunRange = unit.gunController.gun.range;
            var currentDistance = Vector3.Distance(unit.Position, target.Position);

            // Define optimal distance range
            var minDistance = Mathf.Max(1.0f, gunRange * 0.3f); // Minimum distance to avoid getting too close
            var optimalDistance = gunRange * 0.7f; // Optimal distance for targeting

            // If already at good distance, stay put
            if (currentDistance >= minDistance && currentDistance <= optimalDistance)
            {
                return unit.Position;
            }

            // Calculate direction from target to unit
            var directionToUnit = (unit.Position - target.Position).normalized;

            // If unit is too close or direction is invalid, use a default direction
            if (directionToUnit.magnitude < 0.1f)
            {
                // Try different directions to find a good position
                var angles = new[] { 0f, 90f, 180f, 270f, 45f, 135f, 225f, 315f };
                foreach (var angle in angles)
                {
                    var direction = new Vector3(Mathf.Cos(angle * Mathf.Deg2Rad), Mathf.Sin(angle * Mathf.Deg2Rad), 0);
                    var testPosition = target.Position + (direction * optimalDistance);

                    // Use the first direction that doesn't overlap with the target
                    if (Vector3.Distance(testPosition, target.Position) >= minDistance)
                    {
                        directionToUnit = direction;
                        break;
                    }
                }

                // Fallback if no good direction found
                if (directionToUnit.magnitude < 0.1f)
                {
                    directionToUnit = Vector3.right;
                }
            }

            // Calculate optimal position
            var optimalPosition = target.Position + (directionToUnit * optimalDistance);

            return optimalPosition;
        }

        public static IBaseOrder AttackMove(UnitController unit, Vector3 targetPosition, float detectionRadius = 6f)
        {
            var attackMoveOrder = AttackMoveOrder.Get();
            attackMoveOrder.Initialize(unit, targetPosition, detectionRadius, unit);
            return attackMoveOrder;
        }

        public static IBaseOrder StayStill(UnitController unit)
        {
            var stayStillOrder = StayStillOrder.Get();
            stayStillOrder.Initialize(unit, unit.movementController);
            return stayStillOrder;
        }

        public static IBaseOrder Defend(UnitController unit, Vector3 defendPosition, float defendRadius = 5f,
            float detectionRadius = 8f)
        {
            var defendOrder = DefendOrder.Get();
            defendOrder.Initialize(unit, defendPosition, defendRadius, detectionRadius, unit);
            return defendOrder;
        }

        public static IBaseOrder RandomWonder(UnitController unit, Vector3 targetPosition, float radius)
        {
            var random2Dposition = Random.insideUnitCircle * radius;
            var randomPosition = targetPosition + new Vector3(random2Dposition.x, random2Dposition.y, 0f);

            var moveOrder = MoveToPosition(unit, randomPosition);
            moveOrder.OnComplete += () =>
            {
                var randomWonderOrder = RandomWonder(unit, targetPosition, radius);
                unit.unitCommander.IssueOrder(randomWonderOrder);
            };

            return moveOrder;
        }

        /// <summary>
        ///     Move to a dynamic target that can move. Uses TargetTrackingDecorator to continuously update position.
        /// </summary>
        public static IBaseOrder MoveTo(UnitController unit, ITeamDamageable target, float stoppingDistance = 0)
        {
            var totalOffset = MathUtils.CalculateOffset(unit);
            var adjustedStoppingDistance = totalOffset + stoppingDistance;

            // Create base move order with initial target position
            var moveOrder = MoveOrder.Get();
            moveOrder.Initialize(unit, target.Position, adjustedStoppingDistance, unit.movementController);

            // Wrap with target tracking decorator for dynamic position updates
            var trackingDecorator = new TargetTrackingDecorator(moveOrder)
            {
                target = target,
                stoppingDistance = adjustedStoppingDistance,
                updateInterval = 0.2f // Update every 0.2 seconds
            };

            return trackingDecorator;
        }

        /// <summary>
        ///     Move to a static position. Position will not be updated during movement.
        /// </summary>
        public static IBaseOrder MoveToPosition(UnitController unit, Vector3 targetPosition, float stoppingDistance = 0)
        {
            var totalOffset = MathUtils.CalculateOffset(unit);

            var moveOrder = MoveOrder.Get();
            moveOrder.Initialize(unit, targetPosition, totalOffset + stoppingDistance, unit.movementController);

            var distance = Vector3.Distance(unit.transform.position, targetPosition);
            var travelTime = distance / unit.movementController.movementSpeed;

            var timedMoveOrder = new TimedDecorator(moveOrder) { timeLimit = distance / travelTime * 2f };

            return moveOrder;
        }
    }
}
