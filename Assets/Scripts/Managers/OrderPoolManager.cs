using Common;
using System;
using System.Collections.Generic;
using UnitParts.Interfaces;
using UnitParts.Orders;
using UnitParts.Orders.Decorators;
using UnityEngine;

namespace Managers
{
    [DefaultExecutionOrder(-50)]
    public class OrderPoolManager : MonoBehaviour, IOrderPoolManager
    {
        private void Awake()
        {
            // Register this instance in the service locator
            ServiceLocator.Register<IOrderPoolManager>(this);

            // Ensure this manager persists across scenes
            DontDestroyOnLoad(gameObject);
        }

        private void OnDestroy() =>
            // Unregister from service locator when destroyed
            ServiceLocator.Unregister<IOrderPoolManager>();

        // Order pools
        public ObjectPool<MoveOrder> MoverOrderPool { get; } = new();
        public ObjectPool<AttackMoveOrder> AttackMoveOrderPool { get; } = new();
        public ObjectPool<StayStillOrder> StayStillOrderPool { get; } = new();
        public ObjectPool<DefendOrder> DefendOrderPool { get; } = new();
        public ObjectPool<ResourceThiefOrder> ResourceThiefOrderPool { get; } = new();

        // Decorator pools
        public DecoratorPool<TimedDecorator> TimedDecoratorPool { get; } = new();
        public DecoratorPool<TargetTrackingDecorator> TargetTrackingDecoratorPool { get; } = new();
        public DecoratorPool<GunTargetDecorator> GunTargetDecoratorPool { get; } = new();
        public DecoratorPool<EnemyDetectionDecorator> EnemyDetectionDecoratorPool { get; } = new();
        public DecoratorPool<ConditionalDecorator> ConditionalDecoratorPool { get; } = new();
    }
}

[Serializable]
public class ObjectPool<T> where T : IBaseOrder, new()
{
    private readonly Queue<T> _pool = new();

    public T Get()
    {
        // If an instance is available, use it; otherwise create a new one.
        if (_pool.Count > 0)
        {
            return _pool.Dequeue();
        }

        return new T();
    }

    public void Return(T item) =>
        // Optionally, reset the item’s state here if needed.
        _pool.Enqueue(item);
}
/// <summary>
/// Object pool for decorators with proper state reset functionality
/// Note: Due to BaseOrderDecorator design, decorators cannot be easily reused with different wrapped orders
/// </summary>
[Serializable]
public class DecoratorPool<T> where T : BaseOrderDecorator, new()
{
    private readonly Queue<T> _pool = new();

    /// <summary>
    /// Get a decorator instance from the pool or create a new one
    /// Note: Currently creates new instances due to decorator design limitations
    /// </summary>
    public T Get(IBaseOrder wrappedOrder)
    {
        // Due to BaseOrderDecorator design, we cannot reuse decorators with different wrapped orders
        // The wrapped order is set in constructor and cannot be changed
        // For now, we always create new instances
        return (T)Activator.CreateInstance(typeof(T), wrappedOrder);
    }

    /// <summary>
    /// Return a decorator instance to the pool with proper state reset
    /// Note: Currently not used due to decorator design limitations, but prepared for future improvements
    /// </summary>
    public void Return(T item)
    {
        if (item == null) return;

        // Reset decorator state to prevent leakage
        item.OnComplete = null;
        item.OnCancel = null;

        // Reset specific decorator properties based on type
        ResetDecoratorProperties(item);

        _pool.Enqueue(item);
    }

    /// <summary>
    /// Reset decorator-specific properties to default values
    /// </summary>
    private void ResetDecoratorProperties(T decorator)
    {
        switch (decorator)
        {
            case TimedDecorator timed:
                timed.timeLimit = 0f;
                timed.onTimeout = null;
                break;
            case TargetTrackingDecorator tracking:
                tracking.target = null;
                tracking.updateInterval = 0.2f;
                tracking.stoppingDistance = 0f;
                break;
            case GunTargetDecorator gunTarget:
                gunTarget.target = null;
                break;
            case EnemyDetectionDecorator detection:
                detection.detectionRadius = 5f;
                detection.detectionInterval = 0.2f;
                detection.onEnemyDetected = null;
                detection.onNoEnemiesDetected = null;
                break;
            case ConditionalDecorator conditional:
                conditional.condition = null;
                conditional.onCondition = null;
                break;
        }
    }
}
