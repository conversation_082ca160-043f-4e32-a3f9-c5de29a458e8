using UnitParts.Orders;
using UnitParts.Orders.Decorators;

namespace Managers
{
    /// <summary>
    ///     Interface for OrderPoolManager to enable dependency injection
    /// </summary>
    public interface IOrderPoolManager
    {
        /// <summary>
        ///     Pool for move orders
        /// </summary>
        ObjectPool<MoveOrder> MoverOrderPool { get; }

        /// <summary>
        ///     Pool for attack move orders
        /// </summary>
        ObjectPool<AttackMoveOrder> AttackMoveOrderPool { get; }

        /// <summary>
        ///     Pool for stay still orders
        /// </summary>
        ObjectPool<StayStillOrder> StayStillOrderPool { get; }

        /// <summary>
        ///     Pool for defend orders
        /// </summary>
        ObjectPool<DefendOrder> DefendOrderPool { get; }

        /// <summary>
        ///     Pool for resource thief orders
        /// </summary>
        ObjectPool<ResourceThiefOrder> ResourceThiefOrderPool { get; }

        // Decorator pools
        /// <summary>
        ///     Pool for timed decorators
        /// </summary>
        DecoratorPool<TimedDecorator> TimedDecoratorPool { get; }

        /// <summary>
        ///     Pool for target tracking decorators
        /// </summary>
        DecoratorPool<TargetTrackingDecorator> TargetTrackingDecoratorPool { get; }

        /// <summary>
        ///     Pool for gun target decorators
        /// </summary>
        DecoratorPool<GunTargetDecorator> GunTargetDecoratorPool { get; }

        /// <summary>
        ///     Pool for enemy detection decorators
        /// </summary>
        DecoratorPool<EnemyDetectionDecorator> EnemyDetectionDecoratorPool { get; }

        /// <summary>
        ///     Pool for conditional decorators
        /// </summary>
        DecoratorPool<ConditionalDecorator> ConditionalDecoratorPool { get; }
    }
}
